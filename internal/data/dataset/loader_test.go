package dataset

import (
	"os"
	"path/filepath"
	"testing"
)

func TestLoaderBasic(t *testing.T) {
	// Create a temporary CSV file
	csvContent := `name,age,salary,active
<PERSON>,25,50000.5,true
<PERSON>,30,75000.0,false
<PERSON>,35,60000.25,true`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "test.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file: %v", err)
	}

	// Load CSV data
	loader := NewLoader()
	data := loader.LoadCSV(csvFile)
	if data == nil {
		t.Fatal("LoadCSV returned nil data")
	}

	// Verify basic structure
	if data.NumRows != 3 {
		t.Errorf("Expected 3 rows, got %d", data.NumRows)
	}

	if data.NumColumns != 4 {
		t.<PERSON><PERSON><PERSON>("Expected 4 columns, got %d", data.NumColumns)
	}

	expectedHeaders := []string{"name", "age", "salary", "active"}
	for i, expected := range expectedHeaders {
		if data.Headers[i] != expected {
			t.Errorf("Header %d: expected %s, got %s", i, expected, data.Headers[i])
		}
	}

	// Verify some data content
	if len(data.Records) != 3 {
		t.Errorf("Expected 3 records, got %d", len(data.Records))
	}
	if data.Records[0][0] != "John" {
		t.Errorf("Expected first record name to be 'John', got %s", data.Records[0][0])
	}
}

func TestLoaderFileNotFound(t *testing.T) {
	loader := NewLoader()
	data := loader.LoadCSV("nonexistent.csv")

	// Should return nil for nonexistent file
	if data != nil {
		t.Fatal("Expected nil data for nonexistent file, got valid data")
	}
}

func TestLoaderEmptyFile(t *testing.T) {
	// Create an empty CSV file
	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "empty.csv")

	err := os.WriteFile(csvFile, []byte(""), 0644)
	if err != nil {
		t.Fatalf("Failed to create empty CSV file: %v", err)
	}

	loader := NewLoader()
	data := loader.LoadCSV(csvFile)

	// Should return data with zero values for empty CSV
	if data == nil {
		t.Fatal("Expected data to be returned for empty CSV")
	}
	if data.NumColumns != 0 {
		t.Errorf("Expected 0 columns, got %d", data.NumColumns)
	}
	if data.NumRows != 0 {
		t.Errorf("Expected 0 rows, got %d", data.NumRows)
	}
}

func TestLoaderHeaderOnly(t *testing.T) {
	// Create a CSV file with only headers
	csvContent := `name,age,salary,active`
	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "header_only.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create header-only CSV file: %v", err)
	}

	loader := NewLoader()
	data := loader.LoadCSV(csvFile)
	if data == nil {
		t.Fatal("LoadCSV returned nil data")
	}

	// Should have headers but no data rows
	if data.NumColumns != 4 {
		t.Errorf("Expected 4 columns, got %d", data.NumColumns)
	}
	if data.NumRows != 0 {
		t.Errorf("Expected 0 data rows, got %d", data.NumRows)
	}
	if len(data.Headers) != 4 {
		t.Errorf("Expected 4 headers, got %d", len(data.Headers))
	}
	if len(data.Records) != 0 {
		t.Errorf("Expected 0 records, got %d", len(data.Records))
	}
}

package cli

import (
	"fmt"

	"github.com/berrijam/mulberri/internal/config"
	"github.com/berrijam/mulberri/internal/data/dataset"
	"github.com/berrijam/mulberri/internal/data/features"
	"github.com/berrijam/mulberri/internal/utils/logger"
	"github.com/spf13/cobra"
)

// NewTrainCommand creates the train subcommand for building decision trees.
//
// Handles training-specific flags, validation, and delegates to training package.
// Uses defaults from config package and validates all required parameters.
//
// Returns cobra command configured with training flags and validation.
func NewTrainCommand() *cobra.Command {
	cfg := &TrainingConfig{
		MaxDepth:        config.DefaultMaxDepth,
		MinSamplesSplit: config.DefaultMinSamples,
		Criterion:       config.DefaultCriterion,
	}

	trainCmd := &cobra.Command{
		Use:   "train",
		Short: "Train a C4.5 decision tree model",
		Long: `Train a decision tree model from CSV data using the C4.5 algorithm.

The training process builds an interpretable tree structure optimized for
classification tasks with configurable depth and splitting criteria.`,
		Example: `  # Basic training
  mulberri train -i data.csv -t species -o model.dt -f features.yaml

  # Training with custom parameters
  mulberri train -i data.csv -t target -o model.dt -f features.yaml --max-depth 15 --verbose

  # Training with different feature specification
  mulberri train -i data.csv -t target -o model.dt -f custom_features.yaml`,
		RunE: func(cmd *cobra.Command, args []string) error {
			CurrentSubcommand = cmd.Name()

			// Set up logger configuration with verbose flag from CLI
			logConfig := logger.LogConfig{
				LogFolder:     "logs",
				MaxSize:       10,
				EnableConsole: true,
				AppName:       "mulberri",
				EnableColors:  true,
				MaxBackups:    7,
				MaxAge:        7,
				Verbose:       cfg.Verbose,
				Operation:     "train",
			}

			// Initialize logger with the configuration
			if err := logger.SetGlobalConfig(logConfig); err != nil {
				fmt.Printf("Failed to initialize logger: %v\n", err)
				return err
			}

			cfg.Validate()
			runTraining(cfg)
			return nil
		},
	}

	// Required flags
	trainCmd.Flags().StringVarP(&cfg.InputFile, "input", "i", "", "Input CSV file path")
	trainCmd.Flags().StringVarP(&cfg.TargetCol, "target", "t", "", "Target column name")
	trainCmd.Flags().StringVarP(&cfg.OutputFile, "output", "o", "", "Output model file path")
	trainCmd.Flags().StringVarP(&cfg.FeatureInfoFile, "features", "f", "", "Feature info YAML file path")

	// Optional flags with defaults
	trainCmd.Flags().IntVar(&cfg.MaxDepth, "max-depth", cfg.MaxDepth, "Maximum tree depth")
	trainCmd.Flags().IntVar(&cfg.MinSamplesSplit, "min-samples", cfg.MinSamplesSplit, "Minimum samples to split")
	trainCmd.Flags().StringVar(&cfg.Criterion, "criterion", cfg.Criterion, "Split criterion (entropy only)")
	trainCmd.Flags().BoolVarP(&cfg.Verbose, "verbose", "v", false, "Enable verbose output")

	// Mark required flags
	trainCmd.MarkFlagRequired("input")
	trainCmd.MarkFlagRequired("target")
	trainCmd.MarkFlagRequired("output")
	trainCmd.MarkFlagRequired("features")

	return trainCmd
}

// runTraining executes the training workflow with validated configuration.
//
// Args:
// - cfg: Validated training configuration
//
// Workflow:
// 1. Load feature information from YAML file
// 2. Load and validate CSV data
// 3. Validate CSV columns against feature info
// 4. Log dataset statistics
// 5. TODO: Build decision tree model
// 6. TODO: Save trained model
//
// Side effects:
// - Creates log entries for training progress
// - Exits if data loading fails
// - May create output files in future iterations
func runTraining(cfg *TrainingConfig) {
	logger.Info("Starting C4.5 decision tree training")
	logger.Info(fmt.Sprintf("Input: %s, Target: %s, Output: %s",
		cfg.InputFile, cfg.TargetCol, cfg.OutputFile))

	logger.Debug(fmt.Sprintf("Training parameters: max_depth=%d, min_samples=%d, criterion=%s",
		cfg.MaxDepth, cfg.MinSamplesSplit, cfg.Criterion))

	// Step 1: Load feature information from YAML file
	logger.Info("Loading feature information from YAML file")
	featureLoader := features.NewFeatureLoader()
	featureConfig := featureLoader.LoadFeatureInfo(cfg.FeatureInfoFile)

	logger.Info(fmt.Sprintf("Loaded feature info for %d features", len(*featureConfig)))
	for featureName, featureInfo := range *featureConfig {
		logger.Debug(fmt.Sprintf("Feature: %s, type: %s, handle_as: %s",
			featureName, featureInfo.Type, featureInfo.HandleAs))
	}

	// Step 2: Load CSV data
	logger.Info("Loading CSV training data")
	datasetLoader := dataset.NewLoader()
	csvData := datasetLoader.LoadCSV(cfg.InputFile)

	if csvData == nil {
		logger.Fatal("Failed to load CSV data")
		return
	}

	logger.Info(fmt.Sprintf("Loaded CSV data: %d rows, %d columns",
		csvData.NumRows, csvData.NumColumns))

	logger.Debug(fmt.Sprintf("CSV headers: %v", csvData.Headers))

	// Step 3: Validate CSV columns against feature information
	logger.Info("Validating CSV Column names against feature information")
	features.ValidateCSVColumnsAgainstFeatureInfo(featureConfig, csvData.Headers, cfg.TargetCol)
	logger.Info("CSV validation completed successfully")

	// Step 4: Log dataset statistics
	targetColumnFound := false
	for _, header := range csvData.Headers {
		if header == cfg.TargetCol {
			targetColumnFound = true
			break
		}
	}

	if !targetColumnFound {
		logger.Fatal(fmt.Sprintf("Target column '%s' not found in CSV headers", cfg.TargetCol))
		return
	}

	logger.Info(fmt.Sprintf("Target column '%s' found in dataset", cfg.TargetCol))

	// Step 5: Memory cleanup for CSV data
	// Note: In future iterations, CSV data will be converted to typed Dataset before cleanup
	defer func() {
		if csvData != nil {
			csvData.Release()
		}
	}()

	// TODO: Step 6: Convert CSV data to typed Dataset with feature info
	// TODO: Step 7: Build decision tree using C4.5 algorithm
	// TODO: Step 8: Save trained model to output file

	logger.Info("Training preparation completed successfully")
	logger.Info("Note: Tree building and model saving will be implemented in next iteration")

	// Placeholder output for now
	fmt.Printf("Training completed successfully!\n")
	fmt.Printf("- Loaded %d training samples with %d features\n", csvData.NumRows, csvData.NumColumns)
	fmt.Printf("- Feature info validated for %d features\n", len(*featureConfig))
	fmt.Printf("- Target column: %s\n", cfg.TargetCol)
	fmt.Printf("- Model will be saved to: %s (not implemented yet)\n", cfg.OutputFile)
}

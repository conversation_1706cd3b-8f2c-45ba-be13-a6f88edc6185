# Mulberri User Guide

Complete guide for using the Mulberri C4.5 decision tree toolkit.

## Table of Contents

1. [Overview](#overview)
2. [Current Implementation Status](#current-implementation-status)
3. [Getting Started](#getting-started)
4. [Architecture](#architecture)
5. [API Reference](#api-reference)
6. [Development Guide](#development-guide)
7. [Testing](#testing)
8. [Performance](#performance)

## Overview

Mulberri is a high-performance C4.5 decision tree implementation written in Go, designed for production use with emphasis on:

- **Performance**: Optimized for speed and memory efficiency
- **Reliability**: Comprehensive error handling and validation
- **Usability**: Clean CLI interface with detailed logging
- **Maintainability**: Well-structured codebase with extensive tests

## Current Implementation Status

### Phase 1: Data Pipeline (Complete)

**CLI Interface**
- Complete command structure (`train`, `predict`)
- Flag validation and error handling
- Help documentation and examples
- Verbose logging support

**Data Loading & Validation**
- CSV file parsing with header validation
- YAML feature definition loading
- Feature type validation (nominal, numeric, date, datetime, time, binary)
- Column mapping validation between CSV and feature definitions
- Target column validation

**Logging & Error Handling**
- Structured logging with file rotation
- Operation-specific log files
- Comprehensive error messages
- Debug mode support

### Phase 2: Core Algorithm (In Progress)

**C4.5 Implementation**
- [ ] Information gain calculation
- [ ] Tree building and splitting logic
- [ ] Pruning algorithms
- [ ] Continuous attribute handling

**Model Persistence**
- [ ] Model serialization format
- [ ] Model loading for predictions
- [ ] Prediction pipeline

### Phase 3: Advanced Features (Planned)

- [ ] Cross-validation support
- [ ] Feature importance calculation
- [ ] Tree visualization
- [ ] Performance optimizations
- [ ] Parallel processing

## Getting Started

### Prerequisites

- Go 1.23.5 or later
- Git

### Quick Start

1. **Build the application:**
   ```bash
   go build -o mulberri ./cmd/mulberri
   ```

2. **Test with sample data:**
   ```bash
   ./mulberri train \
     --input examples/sample_data.csv \
     --target play_tennis \
     --output tennis_model.dt \
     --features examples/feature_info.yaml \
     --verbose
   ```

3. **Check the results:**
   ```bash
   cat logs/mulberri-train-$(date +%Y-%m-%d).log
   ```

### What You Can Test Right Now

**Data Pipeline (Fully Working)**
- CSV data loading and validation
- YAML feature definition parsing
- Feature type validation
- Column mapping validation
- Target column validation
- Comprehensive logging

**Example Commands**

```bash
# Build Mulberri
go build -o mulberri ./cmd/mulberri

# Test with sample data
./mulberri train \
  --input examples/sample_data.csv \
  --target play_tennis \
  --output tennis_model.dt \
  --features examples/feature_info.yaml \
  --verbose

# Test with larger benchmark dataset
./mulberri train \
  --input benchmark/data/bank_train.csv \
  --target y \
  --output bank_model.dt \
  --features benchmark/data/bank_metadata.yaml \
  --verbose
```

## Architecture

### Project Structure

```
mulberri/
├── cmd/mulberri/              # Application entry point
│   └── main.go               # CLI initialization
├── internal/                 # Internal packages
│   ├── config/              # Configuration and constants
│   │   └── defaults.go      # Default values and supported types
│   ├── data/                # Data handling
│   │   ├── dataset/         # CSV data loading
│   │   └── features/        # YAML feature definitions
│   ├── io/                  # Input/Output operations
│   │   ├── cli/             # Command-line interface
│   │   └── formats/         # File format parsers
│   └── utils/               # Utilities
│       └── logger/          # Structured logging
├── examples/                # Example data and guides
├── benchmark/               # Performance benchmarking
├── scripts/                 # Development scripts
└── test/                    # Integration tests
```

### Key Components

**CLI Layer** (`internal/io/cli/`)
- Command parsing and validation
- Configuration management
- Error handling and user feedback

**Data Layer** (`internal/data/`)
- CSV parsing and validation
- Feature type handling
- Data preprocessing pipeline

**Utils Layer** (`internal/utils/`)
- Structured logging
- Common utilities
- Helper functions

## API Reference

### Command Line Interface

#### Train Command

```bash
mulberri train [flags]
```

**Required Flags:**
- `--input, -i`: CSV file with training data
- `--target, -t`: Name of the target column
- `--output, -o`: Path for the trained model file
- `--features, -f`: YAML file with feature definitions

**Optional Flags:**
- `--max-depth`: Maximum tree depth (default: 10)
- `--min-samples`: Minimum samples to split a node (default: 20)
- `--criterion`: Split criterion, only "entropy" supported (default: entropy)
- `--verbose, -v`: Enable detailed logging

#### Predict Command

```bash
mulberri predict [flags]
```

**Required Flags:**
- `--input, -i`: CSV file with data to predict
- `--model, -m`: Trained model file
- `--output, -o`: Output file for predictions

**Optional Flags:**
- `--verbose, -v`: Enable detailed logging

### Feature Definition Format

YAML format for defining feature types:

```yaml
feature_name:
  type: nominal|numeric|date|datetime|time|binary
  handle_as: string|integer|float
```

**Supported Types:**
- `nominal`: Categorical data
- `numeric`: Numerical data
- `date`, `datetime`, `time`: Date/time data
- `binary`: Boolean/binary data

**Handle As Options:**
- `string`: Text representation
- `integer`: Integer numbers
- `float`: Floating-point numbers

### Example Feature Definition

```yaml
weather:
  type: nominal
  handle_as: string

temperature:
  type: numeric
  handle_as: float

age:
  type: numeric
  handle_as: integer

played_on:
  type: datetime
  handle_as: integer

play_tennis:
  type: binary
  handle_as: string
```

## Development Guide

### Prerequisites

- Go 1.23.5 or later
- Git

### Setup

```bash
# Clone repository
git clone https://github.com/berrijam/mulberri.git
cd mulberri

# Install dependencies
go mod download

# Verify setup
go test ./...
```

### Code Organization

**Package Guidelines:**
- `cmd/`: Application entry points
- `internal/`: Internal packages (not importable by external code)
- `pkg/`: Public packages (when needed)
- `test/`: Integration tests
- `examples/`: Example code and data

**Coding Standards:**
- Follow Go conventions and best practices
- Use structured logging for all operations
- Maintain comprehensive test coverage
- Document public APIs with detailed comments

### Adding New Features

1. **Create feature branch:**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Implement with tests:**
   - Write tests first (TDD approach)
   - Implement functionality
   - Ensure all tests pass

3. **Update documentation:**
   - Update relevant README sections
   - Add examples if applicable
   - Update this guide if needed

4. **Submit for review:**
   - Create pull request
   - Ensure CI passes
   - Address review feedback

## Testing

### Running Tests

```bash
# All tests
go test ./...

# With coverage
go test -cover ./...

# Specific package
go test ./internal/io/cli -v

# Integration tests
go test ./test/integration -v

# Using test script
./scripts/run_tests.sh --coverage --verbose
```

### Test Structure

- **Unit tests**: Test individual functions and methods
- **Integration tests**: Test component interactions
- **End-to-end tests**: Test complete workflows

### Test Data

- Sample datasets in `examples/`
- Benchmark datasets in `benchmark/data/`
- Test fixtures in `test/fixtures/`

## Performance

### Benchmarking

The project includes comprehensive benchmarking against scikit-learn:

```bash
cd benchmark

# Install dependencies
poetry install

# Run benchmarks
poetry run python3 run_benchmark.py --datasets bank --verbose

# Compare implementations (when complete)
poetry run python3 run_benchmark.py \
  --implementation ../cmd/mulberri/mulberri \
  --datasets bank \
  --verbose
```

### Performance Goals

- **Training**: Competitive with scikit-learn on medium datasets
- **Prediction**: Sub-millisecond inference for typical models
- **Memory**: Efficient memory usage for large datasets
- **Scalability**: Handle datasets with 100K+ samples

### Optimization Areas

- Memory-efficient tree representation
- Parallel processing for large datasets
- Optimized splitting algorithms
- Efficient model serialization

## Troubleshooting

### Common Issues

**"Target column not found"**
- Check that your target column name exactly matches a column in your CSV
- Column names are case-sensitive

**"Feature not defined in FeatureInfo"**
- Ensure all CSV columns (except target) are defined in your YAML file
- Or add missing features to your feature_info.yaml

**"File does not exist"**
- Check file paths are correct relative to where you're running the command
- Use absolute paths if needed

### Getting Help

```bash
# Show general help
./mulberri --help

# Show training command help
./mulberri train --help

# Show prediction command help  
./mulberri predict --help
```

### Verbose Logging

Always use `--verbose` when testing to see detailed information:

```bash
./mulberri train \
  --input examples/sample_data.csv \
  --target play_tennis \
  --output tennis_model.dt \
  --features examples/feature_info.yaml \
  --verbose
```

This will show:
- Feature loading details
- CSV parsing information
- Validation steps
- Dataset statistics
- Debug information for troubleshooting

---

For more information, see:
- [README.md](../README.md) - Project overview
- [examples/](../examples/) - Sample data and configurations
- [benchmark/README.md](../benchmark/README.md) - Benchmarking guide
- [scripts/readme.md](../scripts/readme.md) - Development scripts
